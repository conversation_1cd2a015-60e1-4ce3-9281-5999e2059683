---
import Layout from '../../components/core/Layout.astro';
import VenueSearch from '../../components/venues/VenueSearch.tsx';
---

<Layout
  title="Browse Venues - Trodoo"
  description="Discover unique venues for your special events. From intimate gatherings to grand celebrations."
>
  <div class="min-h-screen bg-slate-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-primary-green to-primary-greenLight">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-20 text-center">
          <h1 class="text-5xl font-bold text-white mb-6">
            Find Your Perfect Venue
          </h1>
          <p class="text-xl text-white/90 max-w-2xl mx-auto mb-8">
            Discover unique spaces for your special events. From intimate gatherings to grand celebrations.
          </p>

          <!-- Featured Stats -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">500+</div>
              <div class="text-white/80">Unique Venues</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">50+</div>
              <div class="text-white/80">Cities</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">10k+</div>
              <div class="text-white/80">Happy Events</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <VenueSearch
        client:load
        onVenueSelect={(venue) => {
          window.location.href = `/venues/${venue.id}`;
        }}
      />
    </div>
  </div>
</Layout>

<script>
  // Handle venue navigation
  document.addEventListener('DOMContentLoaded', () => {
    // Any additional client-side functionality can be added here
    console.log('Venue search page loaded');
  });
</script>
