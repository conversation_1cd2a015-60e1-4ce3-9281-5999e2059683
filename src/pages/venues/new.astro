---
import Layout from '../../components/core/Layout.astro';
import VenueForm from '../../components/venues/VenueForm.tsx';
---

<Layout
  title="List Your Venue - Trodoo"
  description="List your property on Trodoo and start earning from your space."
>
  <div class="min-h-screen bg-slate-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-primary-green to-primary-greenLight">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-16 text-center">
          <h1 class="text-4xl font-bold text-white mb-4">
            List Your Venue
          </h1>
          <p class="text-xl text-white/90 max-w-2xl mx-auto">
            Share your space with event hosts and start earning. It's easy to get started.
          </p>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <VenueForm
        client:load
        onSubmit={async (venueData) => {
          // Handle venue creation
          console.log('Venue data:', venueData);
          // In a real app, this would submit to PocketBase
          alert('Venue created successfully! Redirecting to your dashboard...');
          window.location.href = '/dashboard';
        }}
        onCancel={() => {
          window.location.href = '/dashboard';
        }}
      />
    </div>
  </div>
</Layout>

<script>
  // Additional client-side functionality
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Venue creation page loaded');
  });
</script>
