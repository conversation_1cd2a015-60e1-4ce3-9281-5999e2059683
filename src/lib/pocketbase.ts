import PocketBase from 'pocketbase';

// Get PocketBase URL from environment variables
// Use PUBLIC_ prefixed variable for client-side, fallback to server-side variable
const POCKETBASE_URL = import.meta.env.PUBLIC_POCKETBASE_URL || import.meta.env.POCKETBASE_URL || 'https://trodoorentals.pockethost.io';

// console.log('PocketBase URL:', POCKETBASE_URL);

// Create singleton PocketBase client instance
let _pb: PocketBase | null = null;

export function getPocketBase(): PocketBase {
  if (!_pb) {
    _pb = new PocketBase(POCKETBASE_URL);
    
    // Enable auto cancellation for duplicate requests
    _pb.autoCancellation(false);

    // Configure default settings
    _pb.beforeSend = function (url, options) {
      // Add any default headers or configurations here
      return { url, options };
    };
  }

  return _pb;
}

// Export the singleton instance
export const pocketbase = getPocketBase();
export const pb = pocketbase; // Alias for convenience

// Helper functions for common operations
export async function authenticateUser(email: string, password: string) {
  try {
    console.log('Attempting to authenticate user:', email);
    const authData = await pocketbase.collection('users').authWithPassword(email, password);
    console.log('Authentication successful:', authData.record.id);
    return { success: true, user: authData.record, token: authData.token };
  } catch (error) {
    console.error('Authentication failed:', error);

    // Provide more specific error messages
    let errorMessage = 'Authentication failed';
    if (error instanceof Error) {
      if (error.message.includes('Failed to authenticate')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (error.message.includes('verification')) {
        errorMessage = 'Please verify your email address before signing in.';
      } else {
        errorMessage = error.message;
      }
    }

    return { success: false, error: errorMessage };
  }
}

export async function registerUser(email: string, password: string, passwordConfirm: string, name?: string) {
  try {
    const userData = {
      email,
      password,
      passwordConfirm,
      name: name || email.split('@')[0], // Use email prefix as default name
    };

    console.log('Attempting to register user:', { email, name: userData.name });

    const user = await pocketbase.collection('users').create(userData);
    console.log('User created successfully:', user.id);

    // Send verification email (optional, don't fail if this fails)
    try {
      await pocketbase.collection('users').requestVerification(email);
      console.log('Verification email sent');
    } catch (verificationError) {
      console.warn('Failed to send verification email:', verificationError);
      // Don't fail the registration if verification email fails
    }

    return { success: true, user };
  } catch (error) {
    console.error('Registration failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Registration failed' };
  }
}

export function logout() {
  pocketbase.authStore.clear();
}

export function getCurrentUser() {
  return pocketbase.authStore.model;
}

export function isAuthenticated(): boolean {
  return pocketbase.authStore.isValid;
}

// Export types for TypeScript support
export type { RecordModel } from 'pocketbase';
export default pocketbase;
