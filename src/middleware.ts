import { defineMiddleware } from 'astro:middleware';
import { getPocketBase } from './lib/pocketbase.ts';

// Define protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/profile',
  '/bookings',
  '/venues/new',
  '/venues/edit',
  '/venues/manage',
  '/messages',
  '/settings'
];

// Define admin-only routes
const ADMIN_ROUTES = [
  '/admin',
  '/admin/users',
  '/admin/flagged-content',
  '/admin/venues',
  '/admin/reports'
];

// Define routes that should redirect authenticated users (like login/register)
const AUTH_REDIRECT_ROUTES = [
  '/auth/login',
  '/auth/register'
];

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context;
  const pathname = url.pathname;
  
  // Initialize PocketBase client
  const pb = getPocketBase();
  
  // Try to authenticate user from cookies/session
  let isAuthenticated = false;
  let isAdmin = false;
  let user = null;
  
  try {
    // Check if there's an auth token in cookies
    const authCookie = request.headers.get('cookie');
    console.log('Middleware - Checking auth cookie:', authCookie ? 'Present' : 'Not found');

    if (authCookie) {
      // Try to find PocketBase auth cookie with different possible names
      const possibleCookieNames = ['pb_auth', 'pocketbase_auth'];
      let authData = null;

      for (const cookieName of possibleCookieNames) {
        const authMatch = authCookie.match(new RegExp(`${cookieName}=([^;]+)`));
        if (authMatch) {
          try {
            authData = JSON.parse(decodeURIComponent(authMatch[1]));
            console.log('Middleware - Found auth data in cookie:', cookieName);
            break;
          } catch (parseError) {
            console.warn('Middleware - Failed to parse auth cookie:', cookieName, parseError);
          }
        }
      }

      if (authData && authData.token && authData.model) {
        pb.authStore.save(authData.token, authData.model);
        isAuthenticated = pb.authStore.isValid;
        user = pb.authStore.model;
        console.log('Middleware - User authenticated:', user?.id, 'Valid:', isAuthenticated);

        // Check if user is admin (check roles array)
        isAdmin = user?.roles?.includes('admin') || false;
      } else {
        console.log('Middleware - No valid auth data found in cookies');
      }
    }

    // Also check if PocketBase already has valid auth (from previous requests)
    if (!isAuthenticated && pb.authStore.isValid && pb.authStore.model) {
      isAuthenticated = true;
      user = pb.authStore.model;
      isAdmin = user?.roles?.includes('admin') || false;
      console.log('Middleware - Using existing PocketBase auth:', user?.id);
    }

  } catch (error) {
    console.error('Auth middleware error:', error);
    // Clear invalid auth data
    pb.authStore.clear();
  }
  
  // Handle protected routes
  if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
    console.log('Middleware - Accessing protected route:', pathname, 'Authenticated:', isAuthenticated);
    if (!isAuthenticated) {
      console.log('Middleware - Redirecting to login, user not authenticated');
      // Redirect to login with return URL
      const returnUrl = encodeURIComponent(pathname + url.search);
      return redirect(`/auth/login?redirect=${returnUrl}`);
    }
    console.log('Middleware - Access granted to protected route');
  }

  // Handle admin routes
  if (ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      const returnUrl = encodeURIComponent(pathname + url.search);
      return redirect(`/auth/login?redirect=${returnUrl}`);
    }
    if (!isAdmin) {
      // Redirect to dashboard or show 403 error
      return redirect('/dashboard?error=access_denied');
    }
  }
  
  // Handle auth redirect routes (login/register)
  if (AUTH_REDIRECT_ROUTES.some(route => pathname.startsWith(route))) {
    if (isAuthenticated) {
      // Get redirect URL from query params or default to dashboard
      const redirectUrl = url.searchParams.get('redirect') || '/dashboard';
      return redirect(redirectUrl);
    }
  }
  
  // Add user context to locals for use in pages
  context.locals.user = user;
  context.locals.isAuthenticated = isAuthenticated;
  context.locals.isAdmin = isAdmin;
  context.locals.pb = pb;
  
  // Security headers
  const response = await next();
  
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Add CSP header for enhanced security
  const pocketbaseUrl = import.meta.env.PUBLIC_POCKETBASE_URL || import.meta.env.POCKETBASE_URL || 'https://trodoorentals.pockethost.io';
  const cspDirectives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https:",
    "font-src 'self' https://fonts.gstatic.com",
    "connect-src 'self' " + pocketbaseUrl + " http://localhost:8090 https://trodoorentals.pockethost.io",
    "frame-ancestors 'none'"
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', cspDirectives);
  
  return response;
});

// Type definitions for context.locals
declare global {
  namespace App {
    interface Locals {
      user: any;
      isAuthenticated: boolean;
      isAdmin: boolean;
      pb: any;
    }
  }
}
