---
export interface Props {
  title?: string;
  description?: string;
  image?: string;
}

const {
  title = 'Trodoo - Premier Venue Rental Platform',
  description = 'Find and book the perfect venue for your events, meetings, and special occasions. Direct connection between renters and property owners.',
  image = '/images/og-image.jpg'
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site || 'http://localhost:4321');
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- Design System Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <!-- Canonical URL -->
    <link rel="canonical" href={canonicalURL.toString()} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={Astro.url.toString()} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={new URL(image, Astro.site || 'http://localhost:4321').toString()} />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={Astro.url.toString()} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={new URL(image, Astro.site || 'http://localhost:4321').toString()} />

<!-- Alpine.js for interactivity -->
    <script is:inline defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <title>{title}</title>

    <!-- Global Styles -->
    <style>
      @import "/src/styles/global.css";
    </style>

    <!-- Design System CSS Variables -->
    <style>
      :root {
        /* Refined Color Palette */
        --color-primary: #059669;
        --color-primary-light: #10B981;
        --color-primary-dark: #047857;
        --color-primary-subtle: #D1FAE5;
        --color-primary-muted: #6EE7B7;

        --color-secondary: #F59E0B;
        --color-secondary-light: #FBBF24;
        --color-secondary-dark: #D97706;
        --color-secondary-subtle: #FEF3C7;
        --color-secondary-muted: #FCD34D;

        --color-neutral-white: #FFFFFF;
        --color-neutral-light-gray: #F9FAFB;
        --color-neutral-medium-gray: #E5E7EB;
        --color-neutral-dark-gray: #6B7280;
        --color-neutral-charcoal: #374151;
        --color-neutral-black: #1F2937;

        /* Typography */
        --font-primary: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
        --font-body: 'Roboto', 'Helvetica Neue', Arial, sans-serif;

        /* Spacing (8px base unit) */
        --spacing-xs: 4px;
        --spacing-sm: 8px;
        --spacing-md: 16px;
        --spacing-lg: 24px;
        --spacing-xl: 32px;
        --spacing-2xl: 48px;
        --spacing-3xl: 64px;

        /* Border Radius */
        --radius-sm: 4px;
        --radius-md: 8px;
        --radius-lg: 12px;
        --radius-xl: 20px;

        /* Shadows */
        --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
        --shadow-large: 0 10px 20px rgba(0, 0, 0, 0.15);
        --shadow-card: 0 8px 16px rgba(0,0,0,0.1);
      }

      body {
        font-family: var(--font-body);
        color: var(--color-neutral-black);
        background-color: var(--color-neutral-white);
        line-height: 1.5;
      }

      h1, h2, h3, h4, h5, h6 {
        font-family: var(--font-primary);
        color: var(--color-neutral-black);
      }
    </style>
  </head>
  <body>
    <slot />
  </body>
</html>
