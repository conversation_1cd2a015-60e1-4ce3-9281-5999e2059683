import { useState } from 'react';
import type { FormEvent } from 'react';

interface LoginFormData {
  email: string;
  password: string;
}

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

export default function LoginForm({ onSubmit, isLoading = false, error }: LoginFormProps) {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });

  const [validationErrors, setValidationErrors] = useState<Partial<LoginFormData>>({});
  const [showShake, setShowShake] = useState(false);

  const validateForm = (): boolean => {
    const errors: Partial<LoginFormData> = {};

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      // Trigger shake animation for validation errors
      setShowShake(true);
      setTimeout(() => setShowShake(false), 300);
      return;
    }

    try {
      await onSubmit(formData);
    } catch (err) {
      console.error('Login failed:', err);
      // Trigger shake animation for login errors
      setShowShake(true);
      setTimeout(() => setShowShake(false), 300);
    }
  };

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-[#1F2937] mb-2">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] ${
            validationErrors.email ? 'border-red-500' : 'border-[#E5E7EB]'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Enter your email"
          disabled={isLoading}
        />
        {validationErrors.email && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in">{validationErrors.email}</p>
        )}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-[#1F2937] mb-2">
          Password
        </label>
        <input
          type="password"
          id="password"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] ${
            validationErrors.password ? 'border-red-500' : 'border-[#E5E7EB]'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Enter your password"
          disabled={isLoading}
        />
        {validationErrors.password && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in">{validationErrors.password}</p>
        )}
      </div>

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className={`w-full flex justify-center items-center py-3 px-6 border border-transparent rounded-lg text-base font-bold text-[#1F2937] bg-[#F59E0B] hover:bg-[#D97706] focus:outline-none focus:ring-3 focus:ring-[#F59E0B]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] ${
          showShake ? 'animate-[shake_0.15s_ease-in-out_0s_2_normal_both]' : ''
        }`}
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-[#1F2937]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Signing in...
          </>
        ) : (
          'Sign In'
        )}
      </button>
    </form>
  );
}
