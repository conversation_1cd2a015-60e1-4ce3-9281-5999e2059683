import { useState } from 'react';
import type { FormEvent } from 'react';

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  passwordConfirm: string;
  agreeToTerms: boolean;
}

interface RegisterFormProps {
  onSubmit: (data: RegisterFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
}

export default function RegisterForm({ onSubmit, isLoading = false, error }: RegisterFormProps) {
  const [formData, setFormData] = useState<RegisterFormData>({
    name: '',
    email: '',
    password: '',
    passwordConfirm: '',
    agreeToTerms: false,
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showShake, setShowShake] = useState(false);

  const getPasswordStrength = (password: string): PasswordStrength => {
    let score = 0;
    const feedback: string[] = [];

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('At least 8 characters');
    }

    if (/(?=.*[a-z])/.test(password)) {
      score += 1;
    } else {
      feedback.push('One lowercase letter');
    }

    if (/(?=.*[A-Z])/.test(password)) {
      score += 1;
    } else {
      feedback.push('One uppercase letter');
    }

    if (/(?=.*\d)/.test(password)) {
      score += 1;
    } else {
      feedback.push('One number');
    }

    if (/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) {
      score += 1;
      feedback.splice(feedback.indexOf('One special character'), 1);
    } else {
      feedback.push('One special character (optional)');
    }

    let color = '#DC2626'; // red
    if (score >= 4) color = '#059669'; // green
    else if (score >= 3) color = '#F59E0B'; // yellow

    return { score, feedback, color };
  };

  const validatePassword = (password: string): string | null => {
    const strength = getPasswordStrength(password);
    if (strength.score < 4) {
      return 'Password must meet the requirements below';
    }
    return null;
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    const passwordError = validatePassword(formData.password);
    if (passwordError) {
      errors.password = passwordError;
    }

    if (!formData.passwordConfirm) {
      errors.passwordConfirm = 'Please confirm your password';
    } else if (formData.password !== formData.passwordConfirm) {
      errors.passwordConfirm = 'Passwords do not match';
    }

    if (!formData.agreeToTerms) {
      errors.agreeToTerms = 'You must agree to the Terms of Service';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      // Trigger shake animation for validation errors
      setShowShake(true);
      setTimeout(() => setShowShake(false), 300);
      return;
    }

    try {
      await onSubmit(formData);
    } catch (err) {
      console.error('Registration failed:', err);
      // Trigger shake animation for registration errors
      setShowShake(true);
      setTimeout(() => setShowShake(false), 300);
    }
  };

  const handleInputChange = (field: keyof RegisterFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      const newErrors = { ...validationErrors };
      delete newErrors[field];
      setValidationErrors(newErrors);
    }
  };

  const passwordStrength = getPasswordStrength(formData.password);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-[#1F2937] mb-2">
          Full Name
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] ${
            validationErrors.name ? 'border-red-500' : 'border-[#E5E7EB]'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Enter your full name"
          disabled={isLoading}
        />
        {validationErrors.name && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in">{validationErrors.name}</p>
        )}
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-[#1F2937] mb-2">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] ${
            validationErrors.email ? 'border-red-500' : 'border-[#E5E7EB]'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Enter your email"
          disabled={isLoading}
        />
        {validationErrors.email && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in">{validationErrors.email}</p>
        )}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-[#1F2937] mb-2">
          Password
        </label>
        <input
          type="password"
          id="password"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] ${
            validationErrors.password ? 'border-red-500' : 'border-[#E5E7EB]'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Create a password"
          disabled={isLoading}
        />

        {/* Password Strength Indicator */}
        {formData.password && (
          <div className="mt-3 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${(passwordStrength.score / 5) * 100}%`,
                    backgroundColor: passwordStrength.color
                  }}
                />
              </div>
              <span className="text-xs font-medium" style={{ color: passwordStrength.color }}>
                {passwordStrength.score < 3 ? 'Weak' : passwordStrength.score < 4 ? 'Good' : 'Strong'}
              </span>
            </div>
            {passwordStrength.feedback.length > 0 && (
              <div className="text-xs text-[#6B7280]">
                <p className="mb-1">Password must include:</p>
                <ul className="space-y-1">
                  {passwordStrength.feedback.map((item, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <span className="w-1 h-1 bg-[#6B7280] rounded-full"></span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {validationErrors.password && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in">{validationErrors.password}</p>
        )}
      </div>

      <div>
        <label htmlFor="passwordConfirm" className="block text-sm font-medium text-[#1F2937] mb-2">
          Confirm Password
        </label>
        <input
          type="password"
          id="passwordConfirm"
          value={formData.passwordConfirm}
          onChange={(e) => handleInputChange('passwordConfirm', e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] ${
            validationErrors.passwordConfirm ? 'border-red-500' : 'border-[#E5E7EB]'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder="Confirm your password"
          disabled={isLoading}
        />
        {validationErrors.passwordConfirm && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in">{validationErrors.passwordConfirm}</p>
        )}
      </div>

      {/* Terms Agreement */}
      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          id="agreeToTerms"
          checked={formData.agreeToTerms}
          onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
          className="mt-1 h-4 w-4 text-[#059669] border-[#E5E7EB] rounded focus:ring-[#059669] focus:ring-2"
          disabled={isLoading}
        />
        <label htmlFor="agreeToTerms" className="text-sm text-[#6B7280]">
          I agree to the{' '}
          <a href="/terms" className="text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="/privacy" className="text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
            Privacy Policy
          </a>
        </label>
      </div>
      {validationErrors.agreeToTerms && (
        <p className="text-sm text-red-600 animate-fade-in">{validationErrors.agreeToTerms}</p>
      )}

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading || !formData.agreeToTerms}
        className={`w-full flex justify-center items-center py-3 px-6 border border-transparent rounded-lg text-base font-bold text-[#1F2937] bg-[#F59E0B] hover:bg-[#D97706] focus:outline-none focus:ring-3 focus:ring-[#F59E0B]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] ${
          showShake ? 'animate-[shake_0.15s_ease-in-out_0s_2_normal_both]' : ''
        }`}
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-[#1F2937]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating account...
          </>
        ) : (
          'Create Account'
        )}
      </button>
    </form>
  );
}
