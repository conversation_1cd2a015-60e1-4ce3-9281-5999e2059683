import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Search, MapPin, Calendar as CalendarIcon } from "lucide-react"
import { Calendar } from "../ui/calendar.tsx"
import { format } from "date-fns"
import { type DateRange } from "react-day-picker"

export default function HeroSearch() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false)
  const datePickerRef = useRef<HTMLDivElement>(null)
  const [isMobile, setIsMobile] = useState(false)

  const handleCloseDatePicker = () => {
    setIsDatePickerVisible(false)
  }

  // Close date picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setIsDatePickerVisible(false)
      }
    }

    if (isDatePickerVisible) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isDatePickerVisible])

  useEffect(() => {
    const updateSize = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth < 768)
      }
    }
    if (typeof window !== 'undefined') {
      window.addEventListener("resize", updateSize)
      updateSize()
      return () => window.removeEventListener("resize", updateSize)
    }
  }, [])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="w-full max-w-4xl mx-auto relative"
    >
      <div className="relative bg-white/20 backdrop-blur-md rounded-2xl md:rounded-full shadow-2xl p-2 md:p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4 items-center">
          <div className="relative col-span-1">
            <MapPin className="absolute top-1/2 left-4 -translate-y-1/2 text-white/80 text-lg md:text-xl" />
            <input
              type="text"
              placeholder="Where?"
              className="w-full bg-transparent text-white placeholder-white/80 border-none focus:ring-0 focus:outline-none pl-10 md:pl-12 py-2 md:py-3 text-base md:text-lg"
            />
          </div>

          <div className="relative col-span-1" ref={datePickerRef}>
            <CalendarIcon className="absolute top-1/2 left-4 -translate-y-1/2 text-white/80 text-lg md:text-xl" />
            <input
              type="text"
              placeholder="When?"
              value={
                dateRange?.from
                  ? dateRange.to
                    ? `${format(dateRange.from, "LLL dd, y")} - ${format(
                        dateRange.to,
                        "LLL dd, y"
                      )}`
                    : format(dateRange.from, "LLL dd, y")
                  : ""
              }
              onFocus={() => setIsDatePickerVisible(true)}
              readOnly
              className="w-full bg-transparent text-white placeholder-white/80 border-none focus:ring-0 focus:outline-none pl-10 md:pl-12 py-2 md:py-3 text-base md:text-lg cursor-pointer"
            />

            <AnimatePresence>
              {isDatePickerVisible && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute bottom-full left-0 mb-2 z-[9999] bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden"
                  ref={datePickerRef}
                >
                  <Calendar
                    mode="range"
                    defaultMonth={dateRange?.from}
                    selected={dateRange}
                    onSelect={setDateRange}
                    numberOfMonths={2}
                    disabled={{ before: new Date() }}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          <div className="col-span-1">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`w-full font-bold rounded-full py-3 text-base md:text-lg transition-colors duration-300 flex items-center justify-center shadow-lg hover:shadow-xl ${
                isMobile
                  ? "bg-neutral-white text-neutral-black"
                  : "bg-secondary hover:bg-secondary-dark text-neutral-black"
              }`}
            >
              <Search className="mr-2 w-5 h-5" />
              Search
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
