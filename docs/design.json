{"designSystem": {"name": "Modern Venue Rental Platform", "version": "1.1.0", "description": "A comprehensive design system for a sleek, modern venue rental service. It features a clean layout, a vibrant and professional color palette, and user-friendly components designed to build trust and streamline the booking process."}, "brandPersonality": {"tone": "professional, trustworthy, approachable, efficient", "visualStyle": "modern, clean, premium, vibrant", "userExperience": "effortless, confident, reliable, seamless", "targetAudience": "business professionals, organizers, quality-conscious consumers"}, "colorPalette": {"primary": {"green": "#059669", "greenLight": "#10B981", "greenDark": "#047857", "greenSubtle": "#D1FAE5", "greenMuted": "#6EE7B7"}, "secondary": {"yellow": "#F59E0B", "yellowLight": "#FBBF24", "yellowDark": "#D97706", "yellowSubtle": "#FEF3C7", "yellowMuted": "#FCD34D"}, "neutral": {"white": "#FFFFFF", "lightGray": "#F9FAFB", "mediumGray": "#E5E7EB", "darkGray": "#6B7280", "charcoal": "#374151", "black": "#1F2937", "slate": {"50": "#F8FAFC", "100": "#F1F5F9", "200": "#E2E8F0", "300": "#CBD5E1", "400": "#94A3B8", "500": "#64748B", "600": "#475569", "700": "#334155", "800": "#1E293B", "900": "#0F172A"}}, "background": {"gradientPrimary": "linear-gradient(135deg, #059669 0%, #10B981 100%)", "gradientSubtle": "linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%)", "overlayPattern": "Subtle transparent geometric pattern overlay"}}, "typography": {"fontFamily": {"primary": "'<PERSON>pin<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, sans-serif", "body": "'Robot<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, sans-serif", "fallback": "system-ui, -apple-system, sans-serif"}, "fontWeights": {"regular": 400, "medium": 500, "semibold": 600, "bold": 700}, "headings": {"h1": {"fontSize": "48px", "fontWeight": 700, "lineHeight": 1.2, "color": "neutral.white", "usage": "Hero headlines"}, "h2": {"fontSize": "32px", "fontWeight": 600, "lineHeight": 1.3, "color": "neutral.black", "usage": "Section headers"}, "h3": {"fontSize": "20px", "fontWeight": 600, "lineHeight": 1.4, "color": "neutral.black", "usage": "Card titles, feature headers"}}, "body": {"large": {"fontSize": "18px", "fontWeight": 400, "lineHeight": 1.6, "color": "neutral.white", "usage": "Hero descriptions"}, "regular": {"fontSize": "16px", "fontWeight": 400, "lineHeight": 1.5, "usage": "General body text, feature descriptions"}, "small": {"fontSize": "14px", "fontWeight": 500, "lineHeight": 1.4, "usage": "Form labels, secondary text"}}}, "spacing": {"scale": "8px base unit", "values": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px"}}, "borderRadius": {"sm": "4px", "md": "8px", "lg": "12px", "xl": "20px", "full": "9999px"}, "shadows": {"subtle": "0 2px 4px rgba(0, 0, 0, 0.05)", "medium": "0 4px 8px rgba(0, 0, 0, 0.1)", "large": "0 10px 20px rgba(0, 0, 0, 0.15)", "card": "0 8px 16px rgba(0,0,0,0.1)"}, "layout": {"containerWidth": "1200px", "header": {"structure": "Horizontal navigation bar with logo left and links right", "background": "neutral.white", "height": "80px", "shadow": "subtle"}, "hero": {"structure": "Split layout: content & image left, form right", "background": "background.gradientPrimary", "borderRadius": "xl", "padding": "3xl 2xl"}, "features": {"structure": "Three-column grid", "background": "neutral.lightGray", "padding": "3xl 2xl"}}, "components": {"buttons": {"primary": {"background": "secondary.yellow", "color": "neutral.black", "padding": "12px 24px", "borderRadius": "md", "fontWeight": 700, "fontSize": "16px", "hoverState": {"background": "secondary.yellowDark"}}, "secondary": {"background": "transparent", "color": "neutral.white", "border": "2px solid neutral.white", "padding": "10px 22px", "borderRadius": "md", "hoverState": {"background": "neutral.white", "color": "primary.green"}}}, "cards": {"bookingForm": {"background": "neutral.white", "borderRadius": "xl", "shadow": "card", "padding": "xl"}, "feature": {"background": "transparent", "borderRadius": "none", "padding": "lg", "textAlign": "center"}}, "forms": {"fields": {"input": {"borderRadius": "md", "border": "1px solid neutral.mediumGray", "padding": "12px 16px", "fontSize": "16px", "backgroundColor": "neutral.white", "focusState": {"borderColor": "primary.green", "boxShadow": "0 0 0 3px rgba(40, 167, 69, 0.2)"}}, "select": {"appearance": "custom dropdown arrow", "icon": "chevron-down"}, "dateInput": {"iconPosition": "right", "calendarIcon": "calendar"}}, "labels": {"fontSize": "14px", "fontWeight": 500, "color": "neutral.black", "marginBottom": "sm"}}, "navigation": {"style": "clean horizontal links", "fontSize": "16px", "fontWeight": 500, "color": "neutral.dark<PERSON>ray", "spacing": "xl between items", "hoverState": {"color": "primary.green"}, "activeState": {"color": "primary.green", "fontWeight": 700}}, "icons": {"style": "line icons", "strokeWidth": "2px", "size": {"regular": "24px", "feature": "48px"}, "color": "primary.green"}}, "imagery": {"productPhotos": {"style": "High-quality 3D render or professional photography", "lighting": "Clean, studio lighting", "background": "Transparent", "angle": "3/4 view"}}, "animations": {"transitions": {"duration": "0.3s", "easing": "ease-in-out", "properties": ["color", "background-color", "transform", "box-shadow"]}, "hoverEffects": {"buttons": "Subtle lift and background color change", "cards": "Subtle shadow increase"}}, "responsiveDesign": {"breakpoints": {"mobile": "768px", "tablet": "1024px"}, "heroAdaptation": {"mobile": "Stack vertically - content first, then form", "tablet": "Maintain side-by-side with adjusted proportions"}, "featureGrid": {"mobile": "Single column", "tablet": "Three columns"}}}